import { searchUsers } from '../src/dal/searchUsersDal.js';

async function runExamples() {
  try {
    console.log('=== Search Users Examples ===\n');

    console.log('1. Search all users (no criteria):');
    const allUsers = await searchUsers(null, 50, 0);
    console.log(`Found ${allUsers.length} users\n`);

    console.log('2. Search for valuers only:');
    const valuersXml = '<criteria><is_valuer_yn>true</is_valuer_yn></criteria>';
    const valuers = await searchUsers(valuersXml, 50, 0);
    console.log(`Found ${valuers.length} valuers\n`);

    console.log('3. Search for registered valuers only:');
    const registeredValuersXml = '<criteria><is_registered_valuer_yn>true</is_registered_valuer_yn></criteria>';
    const registeredValuers = await searchUsers(registeredValuersXml, 50, 0);
    console.log(`Found ${registeredValuers.length} registered valuers\n`);

    console.log('4. Search for active registered valuers:');
    const activeRegisteredValuersXml = `
      <criteria>
        <is_registered_valuer_yn>true</is_registered_valuer_yn>
        <is_active_yn>true</is_active_yn>
      </criteria>
    `;
    const activeRegisteredValuers = await searchUsers(activeRegisteredValuersXml, 50, 0, 'name');
    console.log(`Found ${activeRegisteredValuers.length} active registered valuers\n`);

    console.log('5. Search by username:');
    const usernameXml = '<criteria><username>QVNZ-STEWARTM</username></criteria>';
    const userByUsername = await searchUsers(usernameXml, 50, 0, 'name');
    console.log(`Found ${userByUsername.length} users with username QVNZ-STEWARTM\n`);

    console.log('6. Paginated search (first 10 users):');
    const paginatedUsers = await searchUsers(null, 10, 0, 'name', false);
    console.log(`Found ${paginatedUsers.length} users in first page\n`);

    console.log('7. Descending order by name:');
    const descendingUsers = await searchUsers(null, 5, 0, 'name', true);
    console.log(`Found ${descendingUsers.length} users in descending order\n`);

    if (allUsers.length > 0) {
      console.log('Sample user structure:');
      console.log(JSON.stringify(allUsers[0], null, 2));
    }

  } catch (error) {
    console.error('Error running examples:', error);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  runExamples();
}
