import { searchUsers } from '../src/dal/searchUsersDal.js';

async function runExamples() {
  try {
    console.log('=== Search Users Examples ===\n');

    console.log('1. Search all users (no criteria):');
    const allUsers = await searchUsers({ pageSize: 50, offset: 0 });
    console.log(`Found ${allUsers.length} users\n`);

    console.log('2. Search for valuers only:');
    const valuers = await searchUsers({ isValuer: true, pageSize: 50, offset: 0 });
    console.log(`Found ${valuers.length} valuers\n`);

    console.log('3. Search for registered valuers only:');
    const registeredValuers = await searchUsers({ isRegisteredValuer: true, pageSize: 50, offset: 0 });
    console.log(`Found ${registeredValuers.length} registered valuers\n`);

    console.log('4. Search for active registered valuers:');
    const activeRegisteredValuers = await searchUsers({
      isRegisteredValuer: true,
      isActive: true,
      pageSize: 50,
      offset: 0,
      orderBy: 'name'
    });
    console.log(`Found ${activeRegisteredValuers.length} active registered valuers\n`);

    console.log('5. Search by username:');
    const userByUsername = await searchUsers({
      username: 'QVNZ-STEWARTM',
      pageSize: 50,
      offset: 0,
      orderBy: 'name'
    });
    console.log(`Found ${userByUsername.length} users with username QVNZ-STEWARTM\n`);

    console.log('6. Paginated search (first 10 users):');
    const paginatedUsers = await searchUsers({
      pageSize: 10,
      offset: 0,
      orderBy: 'name',
      desc: false
    });
    console.log(`Found ${paginatedUsers.length} users in first page\n`);

    console.log('7. Descending order by name:');
    const descendingUsers = await searchUsers({
      pageSize: 5,
      offset: 0,
      orderBy: 'name',
      desc: true
    });
    console.log(`Found ${descendingUsers.length} users in descending order\n`);

    if (allUsers.length > 0) {
      console.log('Sample user structure:');
      console.log(JSON.stringify(allUsers[0], null, 2));
    }

  } catch (error) {
    console.error('Error running examples:', error);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  runExamples();
}
