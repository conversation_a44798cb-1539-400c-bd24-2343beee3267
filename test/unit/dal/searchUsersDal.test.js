import { describe, it } from 'mocha';
import { expect } from 'chai';

describe('searchUsersDal unit tests', () => {
  it('should handle default parameters', () => {
    const options = {};
    const {
      isValuer = null,
      isRegisteredValuer = null,
      isActive = null,
      username = null,
      pageSize = 50,
      offset = 0,
      orderBy = 'id',
      desc = false
    } = options;

    expect(isValuer).to.be.null;
    expect(pageSize).to.equal(50);
    expect(orderBy).to.equal('id');
    expect(desc).to.be.false;
  });

  it('should validate orderBy parameter', () => {
    const validOrderBy = ['id', 'name'];
    expect(validOrderBy).to.include('id');
    expect(validOrderBy).to.include('name');
  });
});
