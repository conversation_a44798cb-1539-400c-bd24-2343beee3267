import { describe, it } from 'mocha';
import { expect } from 'chai';
import { parseStringPromise } from 'xml2js';

describe('searchUsersDal', () => {
  describe('XML parsing logic', () => {
    it('should parse criteria XML correctly', async () => {
      const criteriaXml = '<criteria><is_valuer_yn>true</is_valuer_yn><username>testuser</username></criteria>';
      const parsedXml = await parseStringPromise(criteriaXml);
      const criteria = parsedXml.criteria;

      expect(criteria.is_valuer_yn[0]).to.equal('true');
      expect(criteria.username[0]).to.equal('testuser');
    });

    it('should handle empty criteria XML', async () => {
      const criteriaXml = '<criteria></criteria>';
      const parsedXml = await parseStringPromise(criteriaXml);
      const criteria = parsedXml.criteria;

      expect(criteria).to.satisfy(c => c === '' || typeof c === 'object');
    });

    it('should handle complex criteria XML', async () => {
      const criteriaXml = `
        <criteria>
          <is_registered_valuer_yn>true</is_registered_valuer_yn>
          <is_active_yn>false</is_active_yn>
          <username>QVNZ-STEWARTM</username>
        </criteria>
      `;
      const parsedXml = await parseStringPromise(criteriaXml);
      const criteria = parsedXml.criteria;

      expect(criteria.is_registered_valuer_yn[0].trim()).to.equal('true');
      expect(criteria.is_active_yn[0].trim()).to.equal('false');
      expect(criteria.username[0].trim()).to.equal('QVNZ-STEWARTM');
    });
  });

  describe('Function parameters', () => {
    it('should validate orderBy parameter', () => {
      const validOrderBy = ['id', 'name'];
      expect(validOrderBy).to.include('id');
      expect(validOrderBy).to.include('name');
      expect(validOrderBy).to.not.include('invalid');
    });

    it('should handle pagination parameters', () => {
      const pageSize = 50;
      const offset = 0;

      expect(pageSize).to.be.a('number');
      expect(offset).to.be.a('number');
      expect(pageSize).to.be.greaterThan(0);
      expect(offset).to.be.at.least(0);
    });
  });
});
