import { describe, it } from 'mocha';
import { expect } from 'chai';
import { getRatingValuerUsers } from '../../src/dal/userDal.js';
import { searchUsers } from '../../src/dal/searchUsersDal.js';
import { Logger } from '@quotable-value/logger';

global.logger = new Logger();

describe('Exact Comparison Test', () => {
  it('should return identical user IDs and count for valuer search', async function() {
    this.timeout(60000);
    
    try {
      const storedProcResults = await getRatingValuerUsers();
      
      const jsResults = await searchUsers({
        isValuer: true,
        isActive: true,
        pageSize: 5000,
        offset: 0,
        orderBy: 'name',
        desc: false
      });

      console.log(`\n=== COMPARISON RESULTS ===`);
      console.log(`Stored Procedure: ${storedProcResults.length} users`);
      console.log(`JavaScript Function: ${jsResults.length} users`);
      
      expect(storedProcResults.length).to.equal(jsResults.length);
      
      const spUserIds = storedProcResults
        .map(user => user.externalId)
        .filter(id => id)
        .sort();
        
      const jsUserIds = jsResults
        .map(result => result.user_xml.user.username)
        .filter(username => username)
        .sort();
      
      console.log(`\nFirst 10 SP usernames: ${spUserIds.slice(0, 10).join(', ')}`);
      console.log(`First 10 JS usernames: ${jsUserIds.slice(0, 10).join(', ')}`);
      
      const spSet = new Set(spUserIds);
      const jsSet = new Set(jsUserIds);
      
      const onlyInSP = spUserIds.filter(id => !jsSet.has(id));
      const onlyInJS = jsUserIds.filter(id => !spSet.has(id));
      
      console.log(`\nUsers only in SP: ${onlyInSP.length}`);
      console.log(`Users only in JS: ${onlyInJS.length}`);
      
      if (onlyInSP.length > 0) {
        console.log(`Only in SP (first 5): ${onlyInSP.slice(0, 5).join(', ')}`);
      }
      
      if (onlyInJS.length > 0) {
        console.log(`Only in JS (first 5): ${onlyInJS.slice(0, 5).join(', ')}`);
      }
      
      const matchingUsers = spUserIds.filter(id => jsSet.has(id));
      console.log(`\nMatching users: ${matchingUsers.length}`);
      console.log(`Match percentage: ${((matchingUsers.length / Math.max(spUserIds.length, jsUserIds.length)) * 100).toFixed(2)}%`);
      
      expect(matchingUsers.length).to.be.greaterThan(0);
      
    } catch (error) {
      console.error('Comparison test error:', error);
      throw error;
    }
  });

  it('should verify data structure consistency', async function() {
    this.timeout(30000);
    
    try {
      const jsResults = await searchUsers({
        isValuer: true,
        pageSize: 5,
        offset: 0,
        orderBy: 'name',
        desc: false
      });

      if (jsResults.length > 0) {
        const sample = jsResults[0];
        
        console.log(`\n=== DATA STRUCTURE VERIFICATION ===`);
        console.log(`Sample user structure:`);
        console.log(`- ID: ${sample.user_xml.user.id}`);
        console.log(`- Username: ${sample.user_xml.user.username}`);
        console.log(`- Name: ${sample.user_xml.user.name}`);
        console.log(`- Is Valuer: ${sample.user_xml.user.is_valuer_yn}`);
        console.log(`- Is Active: ${sample.user_xml.user.is_active_yn}`);
        console.log(`- Office: ${sample.user_xml.user.office.name}`);
        console.log(`- Found Count: ${sample.found_count}`);
        
        expect(sample).to.have.property('user_xml');
        expect(sample).to.have.property('found_count');
        expect(sample.user_xml).to.have.property('user');
        expect(sample.user_xml.user).to.have.property('id');
        expect(sample.user_xml.user).to.have.property('username');
        expect(sample.user_xml.user).to.have.property('name');
        expect(sample.user_xml.user).to.have.property('is_valuer_yn');
        expect(sample.user_xml.user).to.have.property('is_active_yn');
        expect(sample.user_xml.user).to.have.property('office');
        expect(sample.user_xml.user.office).to.have.property('name');
      }
      
    } catch (error) {
      console.error('Structure verification error:', error);
      throw error;
    }
  });

  it('should test performance comparison', async function() {
    this.timeout(60000);
    
    try {
      console.log(`\n=== PERFORMANCE COMPARISON ===`);
      
      const spStart = Date.now();
      const storedProcResults = await getRatingValuerUsers();
      const spTime = Date.now() - spStart;
      
      const jsStart = Date.now();
      const jsResults = await searchUsers({
        isValuer: true,
        isActive: true,
        pageSize: 5000,
        offset: 0,
        orderBy: 'name',
        desc: false
      });
      const jsTime = Date.now() - jsStart;
      
      console.log(`Stored Procedure: ${spTime}ms (${storedProcResults.length} users)`);
      console.log(`JavaScript Function: ${jsTime}ms (${jsResults.length} users)`);
      console.log(`Performance ratio: ${(jsTime / spTime).toFixed(2)}x`);
      
      expect(storedProcResults.length).to.be.greaterThan(0);
      expect(jsResults.length).to.be.greaterThan(0);
      
    } catch (error) {
      console.error('Performance test error:', error);
      throw error;
    }
  });
});
