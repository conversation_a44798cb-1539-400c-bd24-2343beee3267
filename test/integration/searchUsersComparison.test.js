import { describe, it } from 'mocha';
import { expect } from 'chai';
import { getRatingValuerUsers } from '../../src/dal/userDal.js';
import { searchUsers } from '../../src/dal/searchUsersDal.js';
import { Logger } from '@quotable-value/logger';

global.logger = new Logger();

describe('Search Users Comparison Tests', () => {
  describe('Comparing stored procedure vs JavaScript implementation', () => {
    it('should return identical results for valuer search', async function() {
      this.timeout(30000);
      
      try {
        const storedProcResults = await getRatingValuerUsers();
        
        const jsResults = await searchUsers({
          isValuer: true,
          isActive: true,
          pageSize: 5000,
          offset: 0,
          orderBy: 'name',
          desc: false
        });

        console.log(`Stored procedure returned ${storedProcResults.length} users`);
        console.log(`JavaScript function returned ${jsResults.length} users`);

        if (storedProcResults.length > 0) {
          console.log('Sample stored procedure result:', JSON.stringify(storedProcResults[0], null, 2));
        }
        
        if (jsResults.length > 0) {
          console.log('Sample JavaScript result:', JSON.stringify(jsResults[0], null, 2));
        }

        expect(jsResults).to.be.an('array');
        expect(storedProcResults).to.be.an('array');
        
      } catch (error) {
        console.error('Test error:', error);
        throw error;
      }
    });

    it('should test basic search functionality', async function() {
      this.timeout(30000);
      
      try {
        const results = await searchUsers({
          pageSize: 10,
          offset: 0,
          orderBy: 'id',
          desc: false
        });

        console.log(`Basic search returned ${results.length} users`);
        
        if (results.length > 0) {
          console.log('Sample result structure:', JSON.stringify(results[0], null, 2));
          
          expect(results[0]).to.have.property('user_xml');
          expect(results[0]).to.have.property('found_count');
          expect(results[0].user_xml).to.have.property('user');
          
          const user = results[0].user_xml.user;
          expect(user).to.have.property('id');
          expect(user).to.have.property('name');
          expect(user).to.have.property('username');
        }

        expect(results).to.be.an('array');
        expect(results.length).to.be.at.most(10);
        
      } catch (error) {
        console.error('Basic search test error:', error);
        throw error;
      }
    });

    it('should test active users filter', async function() {
      this.timeout(30000);
      
      try {
        const activeResults = await searchUsers({
          isActive: true,
          pageSize: 10,
          offset: 0,
          orderBy: 'name',
          desc: false
        });

        console.log(`Active users search returned ${activeResults.length} users`);
        
        if (activeResults.length > 0) {
          const user = activeResults[0].user_xml.user;
          console.log(`Sample active user: ${user.name} (${user.username})`);
          expect(user.is_active_yn).to.equal('true');
        }

        expect(activeResults).to.be.an('array');
        
      } catch (error) {
        console.error('Active users test error:', error);
        throw error;
      }
    });

    it('should test valuer filter', async function() {
      this.timeout(30000);
      
      try {
        const valuerResults = await searchUsers({
          isValuer: true,
          pageSize: 10,
          offset: 0,
          orderBy: 'name',
          desc: false
        });

        console.log(`Valuer search returned ${valuerResults.length} users`);
        
        if (valuerResults.length > 0) {
          const user = valuerResults[0].user_xml.user;
          console.log(`Sample valuer: ${user.name} (${user.username})`);
          expect(user.is_valuer_yn).to.equal('true');
        }

        expect(valuerResults).to.be.an('array');
        
      } catch (error) {
        console.error('Valuer test error:', error);
        throw error;
      }
    });

    it('should test registered valuer filter', async function() {
      this.timeout(30000);
      
      try {
        const registeredValuerResults = await searchUsers({
          isRegisteredValuer: true,
          pageSize: 10,
          offset: 0,
          orderBy: 'name',
          desc: false
        });

        console.log(`Registered valuer search returned ${registeredValuerResults.length} users`);
        
        if (registeredValuerResults.length > 0) {
          const user = registeredValuerResults[0].user_xml.user;
          console.log(`Sample registered valuer: ${user.name} (${user.username})`);
          expect(user.is_registered_valuer_yn).to.equal('true');
        }

        expect(registeredValuerResults).to.be.an('array');
        
      } catch (error) {
        console.error('Registered valuer test error:', error);
        throw error;
      }
    });

    it('should test pagination', async function() {
      this.timeout(30000);
      
      try {
        const page1 = await searchUsers({
          pageSize: 5,
          offset: 0,
          orderBy: 'name',
          desc: false
        });

        const page2 = await searchUsers({
          pageSize: 5,
          offset: 5,
          orderBy: 'name',
          desc: false
        });

        console.log(`Page 1 returned ${page1.length} users`);
        console.log(`Page 2 returned ${page2.length} users`);
        
        if (page1.length > 0 && page2.length > 0) {
          const user1 = page1[0].user_xml.user;
          const user2 = page2[0].user_xml.user;
          console.log(`Page 1 first user: ${user1.name}`);
          console.log(`Page 2 first user: ${user2.name}`);
          
          expect(user1.id).to.not.equal(user2.id);
        }

        expect(page1).to.be.an('array');
        expect(page2).to.be.an('array');
        expect(page1.length).to.be.at.most(5);
        expect(page2.length).to.be.at.most(5);
        
      } catch (error) {
        console.error('Pagination test error:', error);
        throw error;
      }
    });

    it('should test sorting', async function() {
      this.timeout(30000);
      
      try {
        const ascResults = await searchUsers({
          pageSize: 5,
          offset: 0,
          orderBy: 'name',
          desc: false
        });

        const descResults = await searchUsers({
          pageSize: 5,
          offset: 0,
          orderBy: 'name',
          desc: true
        });

        console.log(`Ascending sort returned ${ascResults.length} users`);
        console.log(`Descending sort returned ${descResults.length} users`);
        
        if (ascResults.length > 1) {
          const user1 = ascResults[0].user_xml.user;
          const user2 = ascResults[1].user_xml.user;
          console.log(`Ascending: ${user1.name} -> ${user2.name}`);
        }
        
        if (descResults.length > 1) {
          const user1 = descResults[0].user_xml.user;
          const user2 = descResults[1].user_xml.user;
          console.log(`Descending: ${user1.name} -> ${user2.name}`);
        }

        expect(ascResults).to.be.an('array');
        expect(descResults).to.be.an('array');
        
      } catch (error) {
        console.error('Sorting test error:', error);
        throw error;
      }
    });
  });
});
