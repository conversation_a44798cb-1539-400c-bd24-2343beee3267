import { describe, it } from 'mocha';
import { expect } from 'chai';
import { getRatingValuerUsers } from '../../src/dal/userDal.js';
import { searchUsers } from '../../src/dal/searchUsersDal.js';
import { Logger } from '@quotable-value/logger';

global.logger = new Logger();

describe('searchUsers Tests', () => {
  it('should return identical results to stored procedure for valuer search', async function() {
    this.timeout(60000);
    
    const storedProcResults = await getRatingValuerUsers();
    const jsResults = await searchUsers({
      isValuer: true,
      isActive: true,
      pageSize: 5000,
      offset: 0,
      orderBy: 'name',
      desc: false
    });

    console.log(`Stored procedure: ${storedProcResults.length} users`);
    console.log(`JavaScript function: ${jsResults.length} users`);

    expect(storedProcResults.length).to.equal(jsResults.length);
    
    const spUsernames = storedProcResults
      .map(user => user.externalId)
      .filter(id => id)
      .sort();
        
    const jsUsernames = jsResults
      .map(result => result.user_xml.user.username)
      .filter(username => username)
      .sort();
    
    const spSet = new Set(spUsernames);
    const jsSet = new Set(jsUsernames);
    const matchingUsers = spUsernames.filter(id => jsSet.has(id));
    
    console.log(`Matching users: ${matchingUsers.length}/${spUsernames.length}`);
    expect(matchingUsers.length).to.equal(spUsernames.length);
  });

  it('should return correct data structure', async function() {
    this.timeout(30000);
    
    const results = await searchUsers({
      pageSize: 5,
      offset: 0,
      orderBy: 'id',
      desc: false
    });

    expect(results).to.be.an('array');
    
    if (results.length > 0) {
      const sample = results[0];
      expect(sample).to.have.property('user_xml');
      expect(sample).to.have.property('found_count');
      expect(sample.user_xml).to.have.property('user');
      
      const user = sample.user_xml.user;
      expect(user).to.have.property('id');
      expect(user).to.have.property('username');
      expect(user).to.have.property('name');
      expect(user).to.have.property('office');
    }
  });

  it('should filter by valuer status', async function() {
    this.timeout(30000);
    
    const results = await searchUsers({
      isValuer: true,
      pageSize: 5,
      offset: 0
    });

    expect(results).to.be.an('array');
    
    if (results.length > 0) {
      const user = results[0].user_xml.user;
      expect(user.is_valuer_yn).to.equal('true');
    }
  });

  it('should filter by registered valuer status', async function() {
    this.timeout(30000);
    
    const results = await searchUsers({
      isRegisteredValuer: true,
      pageSize: 5,
      offset: 0
    });

    expect(results).to.be.an('array');
    
    if (results.length > 0) {
      const user = results[0].user_xml.user;
      expect(user.is_registered_valuer_yn).to.equal('true');
    }
  });

  it('should support pagination', async function() {
    this.timeout(30000);
    
    const page1 = await searchUsers({
      pageSize: 3,
      offset: 0,
      orderBy: 'id'
    });

    const page2 = await searchUsers({
      pageSize: 3,
      offset: 3,
      orderBy: 'id'
    });

    expect(page1).to.be.an('array');
    expect(page2).to.be.an('array');
    expect(page1.length).to.be.at.most(3);
    expect(page2.length).to.be.at.most(3);
    
    if (page1.length > 0 && page2.length > 0) {
      const user1Id = page1[0].user_xml.user.id;
      const user2Id = page2[0].user_xml.user.id;
      expect(user1Id).to.not.equal(user2Id);
    }
  });

  it('should support sorting', async function() {
    this.timeout(30000);
    
    const ascResults = await searchUsers({
      pageSize: 3,
      offset: 0,
      orderBy: 'name',
      desc: false
    });

    const descResults = await searchUsers({
      pageSize: 3,
      offset: 0,
      orderBy: 'name',
      desc: true
    });

    expect(ascResults).to.be.an('array');
    expect(descResults).to.be.an('array');
    
    if (ascResults.length > 1) {
      const names = ascResults.map(r => r.user_xml.user.name).filter(n => n);
      if (names.length > 1) {
        expect(names[0] <= names[1]).to.be.true;
      }
    }
  });
});
