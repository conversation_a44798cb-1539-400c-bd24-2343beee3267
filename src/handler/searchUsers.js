import { searchUsers } from '../services/userService.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';

export const handle = wrapHttpHandler(async (event) => {
  const { criteriaXml, pageSize = 50, offset = 0, orderBy = 'id', desc = false } = event.queryStringParameters || {};
  
  const parsedPageSize = parseInt(pageSize, 10);
  const parsedOffset = parseInt(offset, 10);
  const parsedDesc = desc === 'true' || desc === '1';
  
  if (isNaN(parsedPageSize) || parsedPageSize <= 0) {
    return {
      statusCode: 400,
      body: { error: 'Invalid pageSize parameter' }
    };
  }
  
  if (isNaN(parsedOffset) || parsedOffset < 0) {
    return {
      statusCode: 400,
      body: { error: 'Invalid offset parameter' }
    };
  }
  
  if (orderBy && !['id', 'name'].includes(orderBy)) {
    return {
      statusCode: 400,
      body: { error: 'Invalid orderBy parameter. Must be "id" or "name"' }
    };
  }

  return {
    body: await searchUsers(criteriaXml, parsedPageSize, parsedOffset, orderBy, parsedDesc),
  };
});
