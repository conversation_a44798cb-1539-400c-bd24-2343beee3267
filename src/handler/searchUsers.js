import { searchUsers } from '../services/userService.js';
import wrapHttpHandler from '../util/wrapHttpHandler.js';

export const handle = wrapHttpHandler(async (event) => {
  const {
    isValuer,
    isRegisteredValuer,
    isActive,
    username,
    pageSize = 50,
    offset = 0,
    orderBy = 'id',
    desc = false
  } = event.queryStringParameters || {};

  const parsedPageSize = parseInt(pageSize, 10);
  const parsedOffset = parseInt(offset, 10);
  const parsedDesc = desc === 'true' || desc === '1';
  const parsedIsValuer = isValuer === 'true' ? true : isValuer === 'false' ? false : null;
  const parsedIsRegisteredValuer = isRegisteredValuer === 'true' ? true : isRegisteredValuer === 'false' ? false : null;
  const parsedIsActive = isActive === 'true' ? true : isActive === 'false' ? false : null;

  if (isNaN(parsedPageSize) || parsedPageSize <= 0) {
    return {
      statusCode: 400,
      body: { error: 'Invalid pageSize parameter' }
    };
  }

  if (isNaN(parsedOffset) || parsedOffset < 0) {
    return {
      statusCode: 400,
      body: { error: 'Invalid offset parameter' }
    };
  }

  if (orderBy && !['id', 'name'].includes(orderBy)) {
    return {
      statusCode: 400,
      body: { error: 'Invalid orderBy parameter. Must be "id" or "name"' }
    };
  }

  return {
    body: await searchUsers({
      isValuer: parsedIsValuer,
      isRegisteredValuer: parsedIsRegisteredValuer,
      isActive: parsedIsActive,
      username: username || null,
      pageSize: parsedPageSize,
      offset: parsedOffset,
      orderBy,
      desc: parsedDesc
    }),
  };
});
