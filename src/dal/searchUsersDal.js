import { getQivsPool } from './sqlConnectionPool.js';
import mssql from 'mssql';

export async function searchUsers(options = {}) {
  const {
    isValuer = null,
    isRegisteredValuer = null,
    isActive = null,
    username = null,
    pageSize = 50,
    offset = 0,
    orderBy = 'id',
    desc = false
  } = options;

  const pool = await getQivsPool();

  const direction = desc ? -1 : 1;
  const orderCol = orderBy === 'id' ? 1 : orderBy === 'name' ? 2 : 0;

  const foundRowsQuery = `
    WITH foundRows AS (
      SELECT
        CASE @orderCol
          WHEN 1 THEN CAST(u.qv_user_id AS VARBINARY)
          WHEN 2 THEN CAST(e.full_name AS VARBINARY)
          ELSE NULL
        END AS order_value,
        u.qv_user_id AS id
      FROM qv_user u
      LEFT JOIN employee e ON u.employee_id = e.employee_id
      WHERE (
        (SELECT ISNULL(MAX(1), 0)
         FROM employee e2
         JOIN employee_group_type eg ON e2.employee_group_id = eg.employee_group_id
         WHERE e2.employee_id = u.employee_id AND eg.is_valuer = 1) = @isValuer
        OR @isValuer IS NULL
      )
      AND (
        (SELECT ISNULL(MAX(1), 0)
         FROM employee e2
         JOIN employee_group_type eg ON e2.employee_group_id = eg.employee_group_id
         WHERE e2.employee_id = u.employee_id AND eg.code <> 3 AND eg.is_valuer = 1) = @isRegisteredValuer
        OR @isRegisteredValuer IS NULL
      )
      AND (ISNULL(u.active, 0) = @isActive OR @isActive IS NULL)
      AND (u.qv_user = @username OR @username IS NULL)
    ),
    pageRows AS (
      SELECT
        y.row_nbr,
        y.id,
        MAX(y.row_nbr) OVER() AS found_count
      FROM (
        SELECT
          ROW_NUMBER() OVER(ORDER BY order_value) AS row_nbr,
          id
        FROM foundRows
      ) y
    )
    SELECT
      row_nbr,
      id,
      found_count
    FROM pageRows
    WHERE (@desc = 0 AND row_nbr >= @offset + 1 AND row_nbr <= @offset + @pageSize)
       OR (@desc = 1 AND row_nbr <= found_count - @offset AND row_nbr >= found_count - @offset - @pageSize + 1)
    ORDER BY row_nbr * @direction
  `;

  try {
    const request = pool.request()
      .input('orderCol', mssql.Int, orderCol)
      .input('isValuer', mssql.Bit, isValuer)
      .input('isRegisteredValuer', mssql.Bit, isRegisteredValuer)
      .input('isActive', mssql.Bit, isActive)
      .input('username', mssql.VarChar, username)
      .input('desc', mssql.Bit, desc ? 1 : 0)
      .input('offset', mssql.Int, offset)
      .input('pageSize', mssql.Int, pageSize)
      .input('direction', mssql.Int, direction);

    const result = await request.query(foundRowsQuery);

    if (result.recordset.length === 0) {
      return [];
    }

    const userResults = [];
    for (const row of result.recordset) {
      const userXml = await getUserXml(pool, row.id);
      userResults.push({
        user_xml: userXml,
        found_count: row.found_count
      });
    }

    return userResults;
  } catch (error) {
    throw new Error(`Failed to search users: ${error.message}`);
  }
}

async function getUserXml(pool, userId) {
  const query = `
    SELECT
      u.qv_user_id AS id,
      u.qv_user AS username,
      e.nt_user_name,
      e.full_name AS name,
      e.email_address,
      e.qualifications,
      e.code AS employee_code,
      u.last_login AS last_login_date,
      CASE WHEN u.active = 1 AND LEN(u.qv_user) > 0 THEN 'true' ELSE 'false' END AS is_active_yn,
      CASE WHEN u.locked_out = 1 THEN 'true' ELSE 'false' END AS is_locked_out_yn,
      CASE WHEN u.must_change_password = 1 THEN 'true' ELSE 'false' END AS must_change_password_yn,
      CASE WHEN u.cannot_change_password = 1 THEN 'true' ELSE 'false' END AS cannot_change_password_yn,
      u.failed_logins AS failed_logins_count,
      CASE WHEN EXISTS(
        SELECT 1 FROM employee_group_type eg
        WHERE eg.employee_group_id = e.employee_group_id AND eg.is_valuer = 1
      ) THEN 'true' ELSE 'false' END AS is_valuer_yn,
      CASE WHEN EXISTS(
        SELECT 1 FROM employee_group_type eg
        WHERE eg.employee_group_id = e.employee_group_id AND eg.is_valuer = 1 AND eg.code <> '3'
      ) THEN 'true' ELSE 'false' END AS is_registered_valuer_yn,
      o.qv_office_id AS office_id,
      RTRIM(o.qv_office_code) AS office_code,
      RTRIM(o.qv_office_name) AS office_name,
      RTRIM(o.phone_number_day) AS office_phone_number,
      RTRIM(o.fax_number) AS office_fax_number,
      RTRIM(o.line_1_text) AS office_address_line_1,
      RTRIM(o.line_2_text) AS office_address_line_2,
      RTRIM(o.line_3_text) AS office_address_line_3,
      RTRIM(o.line_4_text) AS office_address_line_4,
      RTRIM(o.line_5_text) AS office_address_line_5,
      RTRIM(o.post_code) AS office_post_code
    FROM qv_user u
    LEFT JOIN employee e ON u.employee_id = e.employee_id
    LEFT JOIN qv_office o ON e.qv_office_id = o.qv_office_id
    WHERE u.qv_user_id = @userId
  `;

  try {
    const result = await pool.request()
      .input('userId', mssql.Int, userId)
      .query(query);

    if (result.recordset.length === 0) {
      return null;
    }

    const user = result.recordset[0];

    const userXml = {
      user: {
        id: user.id,
        username: user.username,
        nt_user_name: user.nt_user_name,
        name: user.name,
        email_address: user.email_address,
        qualifications: user.qualifications,
        employee_code: user.employee_code,
        last_login_date: user.last_login_date,
        is_active_yn: user.is_active_yn,
        is_locked_out_yn: user.is_locked_out_yn,
        must_change_password_yn: user.must_change_password_yn,
        cannot_change_password_yn: user.cannot_change_password_yn,
        failed_logins_count: user.failed_logins_count,
        is_valuer_yn: user.is_valuer_yn,
        is_registered_valuer_yn: user.is_registered_valuer_yn,
        office: {
          id: user.office_id,
          code: user.office_code,
          name: user.office_name,
          phone_number: user.office_phone_number,
          fax_number: user.office_fax_number,
          address_line_1: user.office_address_line_1,
          address_line_2: user.office_address_line_2,
          address_line_3: user.office_address_line_3,
          address_line_4: user.office_address_line_4,
          address_line_5: user.office_address_line_5,
          post_code: user.office_post_code
        }
      }
    };

    return userXml;
  } catch (error) {
    throw new Error(`Failed to get user XML: ${error.message}`);
  }
}
