# Search Users Function

This document describes the Node.js implementation of the `spd_MONARCH_searchUsers` stored procedure.

## Overview

The `searchUsers` function provides identical functionality to the SQL stored procedure `spd_MONARCH_searchUsers.sql`, allowing you to search and filter users with pagination and sorting capabilities.

## Function Signature

```javascript
async function searchUsers(criteriaXml, pageSize, offset, orderBy = 'id', desc = false)
```

### Parameters

- **criteriaXml** (string|null): XML string containing search criteria
- **pageSize** (number): Number of results per page
- **offset** (number): Number of records to skip (for pagination)
- **orderBy** (string): Sort column - either 'id' or 'name' (default: 'id')
- **desc** (boolean): Sort direction - true for descending, false for ascending (default: false)

### Criteria XML Format

The criteria XML supports the following filters:

```xml
<criteria>
  <is_valuer_yn>true|false</is_valuer_yn>
  <is_registered_valuer_yn>true|false</is_registered_valuer_yn>
  <is_active_yn>true|false</is_active_yn>
  <username>username_to_search</username>
</criteria>
```

All criteria elements are optional. If not provided or empty, that filter is ignored.

## Return Value

Returns an array of objects with the following structure:

```javascript
[
  {
    user_xml: {
      user: {
        id: number,
        username: string,
        nt_user_name: string,
        name: string,
        email_address: string,
        qualifications: string,
        employee_code: string,
        last_login_date: Date,
        is_active_yn: 'true'|'false',
        is_locked_out_yn: 'true'|'false',
        must_change_password_yn: 'true'|'false',
        cannot_change_password_yn: 'true'|'false',
        failed_logins_count: number,
        is_valuer_yn: 'true'|'false',
        is_registered_valuer_yn: 'true'|'false',
        office: {
          id: number,
          code: string,
          name: string,
          phone_number: string,
          fax_number: string,
          address_line_1: string,
          address_line_2: string,
          address_line_3: string,
          address_line_4: string,
          address_line_5: string,
          post_code: string
        }
      }
    },
    found_count: number
  }
]
```

## Usage Examples

### Basic Search (All Users)

```javascript
import { searchUsers } from '../src/dal/searchUsersDal.js';

const allUsers = await searchUsers(null, 50, 0);
```

### Search for Valuers Only

```javascript
const criteriaXml = '<criteria><is_valuer_yn>true</is_valuer_yn></criteria>';
const valuers = await searchUsers(criteriaXml, 50, 0);
```

### Search for Active Registered Valuers

```javascript
const criteriaXml = `
  <criteria>
    <is_registered_valuer_yn>true</is_registered_valuer_yn>
    <is_active_yn>true</is_active_yn>
  </criteria>
`;
const activeRegisteredValuers = await searchUsers(criteriaXml, 50, 0, 'name');
```

### Search by Username

```javascript
const criteriaXml = '<criteria><username>QVNZ-STEWARTM</username></criteria>';
const user = await searchUsers(criteriaXml, 50, 0, 'name');
```

### Paginated Search

```javascript
const page1 = await searchUsers(null, 10, 0, 'name', false);
const page2 = await searchUsers(null, 10, 10, 'name', false);
```

### Descending Order

```javascript
const usersDesc = await searchUsers(null, 50, 0, 'name', true);
```

## Database Tables

The function queries the following tables from the legacy QIVS schema:
- `qv_user` - Main user table
- `employee` - Employee details
- `employee_group_type` - Employee group classifications
- `qv_office` - Office information

**Note**: This function is designed to work with the legacy QIVS database schema. The current test environment uses a different schema (`monarch.user`, `monarch.office`, etc.). For production use, ensure you're connecting to the correct database with the legacy schema.

## Error Handling

The function throws errors in the following cases:
- Invalid XML criteria format
- Database connection issues
- SQL query execution errors

## Equivalent SQL Stored Procedure

This function replicates the behavior of:
```sql
EXEC spd_MONARCH_searchUsers @criteriaXml, @pageSize, @offset, @orderBy, @desc
```

## Testing

Run the test suite:
```bash
npm test -- test/unit/dal/searchUsersDal.test.js
```

## Performance Notes

- Uses CTE (Common Table Expression) for improved performance
- Implements efficient pagination without using `COUNT(*) OVER()`
- Supports indexed sorting on user ID and employee name
- Filters are applied at the database level for optimal performance
